"""
Main validation engine that orchestrates all validation operations
"""

from typing import Dict, Any, List
from .enums import ValidationType
from .constants import ValidationConstants
from validators.regex_validator import RegexValidator
from validators.expression_validator import ExpressionValidator


class ValidationEngine:
    """Main validation engine that coordinates all validation operations"""
    
    def __init__(self):
        """Initialize the validation engine with validators"""
        self.regex_validator = RegexValidator()
        self.expression_validator = ExpressionValidator()
    
    def validate_field(self, field_value: Any, field_config: Dict, config: Dict, 
                      all_data: Dict = None, current_group: str = None, 
                      current_field: str = None) -> List[str]:
        """
        Main field validation method that routes to specific validation types
        
        Args:
            field_value: The value to validate
            field_config: Configuration for the field
            config: Global configuration
            all_data: All data for cross-field validation
            current_group: Current group name
            current_field: Current field name
            
        Returns:
            List of error messages (empty if valid)
        """
        field_errors = []
        
        # Handle N/A values
        if field_value == "N/A":
            field_value = ""
        
        # Check required field validation
        if field_config.get("required", False) and (field_value is None or field_value == ""):
            field_errors.append(ValidationConstants.ERROR_MESSAGES["REQUIRED_FIELD"])
        
        # Skip further validation if field is not required and empty
        if not field_config.get("required", False) and (field_value is None or field_value == ""):
            return field_errors
        
        # Process validation rules
        validation_rules = field_config.get("validation_rules", [])
        for rule in validation_rules:
            validation_type = rule.get("isValidationType")
            
            try:
                validation_enum = ValidationType(validation_type)
            except ValueError:
                supported_types = [vt.value for vt in ValidationType]
                error_msg = ValidationConstants.ERROR_MESSAGES["UNSUPPORTED_VALIDATION_TYPE"].format(
                    type=validation_type, 
                    supported_types=supported_types
                )
                field_errors.append(error_msg)
                continue
            
            # Route to appropriate validator
            is_valid, error_msg = self._route_validation(
                validation_enum, field_value, rule, config, all_data, current_group, current_field
            )
            
            if not is_valid:
                field_errors.append(error_msg)
                break  # Stop on first validation failure
        
        return field_errors
    
    def _route_validation(self, validation_type: ValidationType, field_value: Any, 
                         rule: Dict, config: Dict, all_data: Dict, 
                         current_group: str, current_field: str) -> tuple:
        """
        Route validation to the appropriate validator based on type
        
        Returns:
            Tuple of (is_valid: bool, error_message: str)
        """
        match validation_type:
            case ValidationType.REGEX:
                return self.regex_validator.validate_regex(field_value, rule)
            
            case ValidationType.REGEX_LIST:
                return self.regex_validator.validate_regex_list(field_value, rule)
            
            case ValidationType.EXPRESSION_TYPE:
                return self.expression_validator.validate_expression(
                    field_value, rule, config, all_data, current_group, current_field
                )
            
            case ValidationType.EXPRESSION_TYPE_LIST:
                return self.expression_validator.validate_expression_list(
                    field_value, rule, config, all_data, current_group, current_field
                )
            
            case _:
                return False, f"Unsupported validation type: {validation_type.value}"

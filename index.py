# -*- coding: utf-8 -*-
import json
from typing import Dict, <PERSON><PERSON>
from db_utils import DatabaseClient
from core.validation_engine import ValidationEngine


class WorksheetValidator:
    """Main validator class that orchestrates worksheet validation using the new ValidationEngine"""

    def __init__(self, db_client: DatabaseClient = None):
        self.config_data = None
        self.db_client = db_client or DatabaseClient()
        self.validation_engine = ValidationEngine()


    def validate_worksheet_data(self, data: dict, config_key: str, config: Dict) -> Tuple[bool, dict]:
        """Validate worksheet data against configuration rules"""
        config_data = self._extract_validation_config(config_key, config)
        if not config_data:
            return False, {"error": f"Configuration not found for key: {config_key}"}

        updated_data = data.copy()
        all_valid = True

        groups = data.get("groups", {})

        for group_name, group_data in groups.items():
            if "bre_exceptions" not in updated_data["groups"][group_name]:
                updated_data["groups"][group_name]["bre_exceptions"] = {}

            fields = group_data.get("fields", {})

            for field_name, field_data in fields.items():
                field_value = field_data.get("value") if isinstance(field_data, dict) else field_data

                field_config = self._find_field_config(field_name, config_data)
                if not field_config:
                    continue

                field_errors = self.validation_engine.validate_field(
                    field_value, field_config, config_data, data, group_name, field_name
                )

                if field_errors:
                    all_valid = False
                    updated_data["groups"][group_name]["bre_exceptions"][field_name] = "; ".join(field_errors)

        return all_valid, updated_data

    def _extract_validation_config(self, config_key: str, config: Dict) -> Dict:
        """Extract validation configuration for the given key"""
        return config.get(config_key, {})

    def _find_field_config(self, field_name: str, config_data: Dict) -> Dict:
        """Find field configuration in the config data"""
        fields_config = config_data.get("fields", {})
        return fields_config.get(field_name, {})

def example_worksheet_usage():
    """Example usage of the WorksheetValidator"""
    try:
        # Load worksheet data
        try:
            with open("test_workSheet.json", "r") as f:
                worksheet_data = json.load(f)
        except FileNotFoundError:
            print("❌ test_workSheet.json file not found")
            return None
        except json.JSONDecodeError:
            print("❌ Invalid JSON in test_workSheet.json")
            return None

        client = DatabaseClient()
        validator = WorksheetValidator(client)

        # Load local config file
        try:
            with open("collection_data.json", "r") as f:
                config_data = json.load(f)
        except FileNotFoundError:
            print("❌ collection_data.json file not found")
            return None
        except json.JSONDecodeError:
            print("❌ Invalid JSON in collection_data.json")
            return None

        key = "properties"
        is_valid, updated_data = validator.validate_worksheet_data(worksheet_data, key, config_data)

        if is_valid:
            print("✅ Worksheet validation passed!")
        else:
            print("❌ Worksheet validation failed. Check bre_exceptions in groups:")

            for group_name, group_data in updated_data.get("groups", {}).items():
                exceptions = group_data.get("bre_exceptions", {})
                if exceptions:
                    print(f"\n  Group '{group_name}' exceptions:")
                    for field, error in exceptions.items():
                        print(f"    - {field}: {error}")

        with open("validated_output.json", "w") as f:
            json.dump(updated_data, f, indent=2)
        print(f"\n💾 Updated data with bre_exceptions saved to 'validated_output.json'")

        return updated_data

    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return None


if __name__ == "__main__":
    example_worksheet_usage()
#!/usr/bin/env python3
"""
Test script to verify that the import issues are resolved
"""

def test_imports():
    """Test that all imports work correctly"""
    try:
        # Test core imports
        from core.validation_engine import ValidationEngine
        from core.enums import ValidationType
        from core.constants import ValidationConstants
        print("✅ Core imports successful")
        
        # Test validator imports
        from validators.regex_validator import RegexValidator
        from validators.expression_validator import ExpressionValidator
        from validators.base_validator import BaseValidator
        print("✅ Validator imports successful")
        
        # Test creating instances
        engine = ValidationEngine()
        regex_validator = RegexValidator()
        print("✅ Instance creation successful")
        
        print("\n🎉 All imports are working correctly!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    test_imports()

#!/usr/bin/env python3
"""
Simple Database Client

A lightweight utility for connecting to MongoDB and retrieving configuration data.
"""

import os
from typing import Dict, Optional

try:
    from pymongo import MongoClient
    PYMONGO_AVAILABLE = True
except ImportError:
    PYMONGO_AVAILABLE = False


class DatabaseClient:
    """Simple database client for configuration retrieval"""

    def __init__(self, connection_string: str = None, database_name: str = None, collection_name: str = None):
        # Default configuration
        self.connection_string = connection_string or os.getenv("MONGODB_CONNECTION_STRING", "mongodb://localhost:27017/")
        self.database_name = database_name or os.getenv("MONGODB_DATABASE_NAME", "tag_title")
        self.collection_name = collection_name or os.getenv("MONGODB_COLLECTION_NAME", "json_storage")
        
        self.client = None
        self.db = None
        self.collection = None
        self._connected = False

    def connect(self) -> bool:
        """Establish database connection"""
        if not PYMONGO_AVAILABLE:
            print("Error: pymongo not available")
            return False
        
        try:
            self.client = MongoClient(self.connection_string, serverSelectionTimeoutMS=5000)
            # Test connection
            self.client.admin.command('ping')
            self.db = self.client[self.database_name]
            self.collection = self.db[self.collection_name]
            self._connected = True
            return True
        except Exception as e:
            print(f"Database connection failed: {e}")
            return False

    def get_config(self, key: str) -> Optional[Dict]:
        """Get configuration data by key"""
        if not self._connected:
            if not self.connect():
                return None

        try:
            # Try to find by _id first
            result = self.collection.find_one({"_id": key})
            if result:
                return result

            # Try to find by validation_type_key
            result = self.collection.find_one({"validation_type_key": key})
            if result:
                return result

            # Try to find in properties
            result = self.collection.find_one({f"properties.{key}": {"$exists": True}})
            return result

        except Exception as e:
            print(f"Error retrieving config for key '{key}': {e}")
            return None

    def close(self):
        """Close database connection"""
        if self.client:
            self.client.close()
            self._connected = False

    def __enter__(self):
        """Context manager entry"""
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.close()


# Usage example:
if __name__ == "__main__":
    # Example usage
    client = DatabaseClient()
    
    if client.connect():
        config = client.get_config("your_key_here")
        client.close()
   